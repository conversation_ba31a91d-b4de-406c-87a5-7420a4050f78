.game-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.status {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.status p {
    margin: 0;
    padding: 8px 12px;
    background-color: #f0f0f0;
    border-radius: 6px;
    font-weight: 500;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

button, select {
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    background-color: #4CAF50;
    color: white;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #45a049;
}

select {
    background-color: #2196F3;
}

select:hover {
    background-color: #1976D2;
}

.board-container {
    display: flex;
    justify-content: center;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 10px;
}

.board {
    display: grid;
    grid-template-columns: repeat(15, 30px);
    grid-template-rows: repeat(15, 30px);
    gap: 1px;
    background-color: #8b6e4f;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.cell {
    width: 30px;
    height: 30px;
    background-color: #d1b685;
    cursor: pointer;
    position: relative;
    border-radius: 2px;
    transition: background-color 0.2s ease;
}

.cell:hover {
    background-color: #c2a375;
}

.cell.black::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #000;
    top: 3px;
    left: 3px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    animation: placeStone 0.3s ease-out;
}

.cell.white::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid #ccc;
    top: 3px;
    left: 3px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: placeStone 0.3s ease-out;
}

@keyframes placeStone {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.win-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px 50px;
    font-size: 24px;
    border-radius: 10px;
    display: none;
    z-index: 1000;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .game-container {
        padding: 10px;
    }
    
    .board {
        grid-template-columns: repeat(15, 25px);
        grid-template-rows: repeat(15, 25px);
        padding: 8px;
    }
    
    .cell {
        width: 25px;
        height: 25px;
    }
    
    .cell.black::after, .cell.white::after {
        width: 19px;
        height: 19px;
        top: 3px;
        left: 3px;
    }
    
    .status {
        flex-direction: column;
        align-items: center;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    button, select {
        width: 200px;
    }
}

@media (max-width: 600px) {
    .board {
        grid-template-columns: repeat(15, 25px);
        grid-template-rows: repeat(15, 25px);
    }
    .cell {
        width: 25px;
        height: 25px;
    }
    .cell.black::after, .cell.white::after {
        width: 20px;
        height: 20px;
    }
}


/* 在现有CSS基础上添加以下样式 */

.cell.winning {
    background-color: #ffd700 !important;
    animation: winningGlow 1s ease-in-out infinite alternate;
}

.cell.winning.black::after {
    box-shadow: 0 0 15px #ffd700, 0 2px 4px rgba(0,0,0,0.3);
    border: 2px solid #ffd700;
}

.cell.winning.white::after {
    box-shadow: 0 0 15px #ffd700, 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid #ffd700;
}

@keyframes winningGlow {
    0% {
        background-color: #ffd700;
        box-shadow: 0 0 5px #ffd700;
    }
    100% {
        background-color: #ffed4e;
        box-shadow: 0 0 20px #ffd700;
    }
}

/* 获胜线条效果 */
.winning-line {
    position: relative;
    z-index: 10;
}

.winning-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulseWin 2s ease-in-out infinite;
}

@keyframes pulseWin {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
}