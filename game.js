document.addEventListener('DOMContentLoaded', () => {
    // 游戏配置
    const CONFIG = {
        BOARD_SIZE: 15,
        WIN_COUNT: 5,
        MATCH_WINS: 2,
        AI_DELAY: 500
    };

    // 游戏状态
    let gameState = {
        board: Array(CONFIG.BOARD_SIZE).fill().map(() => Array(CONFIG.BOARD_SIZE).fill(null)),
        currentPlayer: 'black',
        gameActive: true,
        level: 'easy',
        gameCount: 1,
        playerWins: 0,
        aiWins: 0,
        gameHistory: []
    };

    // DOM元素缓存
    const elements = {
        board: document.getElementById('board'),
        message: document.getElementById('message'),
        level: document.getElementById('level'),
        gameCount: document.getElementById('game-count'),
        playerWins: document.getElementById('player-wins'),
        aiWins: document.getElementById('ai-wins'),
        startBtn: document.getElementById('start-btn'),
        saveBtn: document.getElementById('save-btn'),
        loadBtn: document.getElementById('load-btn'),
        levelSelect: document.getElementById('level-select')
    };

    // 初始化棋盘
    function initBoard() {
        elements.board.innerHTML = '';
        elements.board.style.gridTemplateColumns = `repeat(${CONFIG.BOARD_SIZE}, 30px)`;
        elements.board.style.gridTemplateRows = `repeat(${CONFIG.BOARD_SIZE}, 30px)`;
        
        for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
            for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                cell.dataset.row = i;
                cell.dataset.col = j;
                cell.addEventListener('click', handleCellClick);
                elements.board.appendChild(cell);
            }
        }
    }

    // 处理单元格点击 - 修复版本
    function handleCellClick(e) {
        if (!gameState.gameActive || gameState.currentPlayer !== 'black') return;

        const row = parseInt(e.target.dataset.row);
        const col = parseInt(e.target.dataset.col);

        if (gameState.board[row][col]) return;

        // 玩家落子
        makeMove(row, col, gameState.currentPlayer);
        gameState.gameHistory.push({row, col, player: gameState.currentPlayer});

        // 检查玩家是否获胜
        if (checkWin(row, col, gameState.currentPlayer)) {
            elements.message.textContent = '🎉 恭喜你获胜了！';
            gameState.playerWins++;
            updateScore();
            gameState.gameActive = false;
            
            // 延迟显示结果，让玩家看到获胜棋子
            setTimeout(() => {
                checkMatchEnd();
            }, 2000);
            return;
        }

        // 检查是否平局
        if (checkDraw()) {
            elements.message.textContent = '⚖️ 平局！';
            gameState.gameActive = false;
            setTimeout(nextGame, 2000);
            return;
        }

        // 切换到AI回合
        gameState.currentPlayer = 'white';
        elements.message.textContent = '🤖 AI思考中...';

        // AI延迟落子
        setTimeout(() => {
            aiMove();
        }, CONFIG.AI_DELAY);
    }

    // 落子函数
    function makeMove(row, col, player) {
        gameState.board[row][col] = player;
        const cell = elements.board.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        cell.classList.add(player);
    }

    // 检查胜利条件
    function checkWin(row, col, player) {
        const directions = [
            [0, 1],  // 水平
            [1, 0],  // 垂直
            [1, 1],  // 对角线
            [1, -1]  // 反对角线
        ];

        for (const [dx, dy] of directions) {
            const winningPositions = [{row, col}];
            let count = 1;

            // 正方向检查
            for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
                const newRow = row + dx * i;
                const newCol = col + dy * i;
                if (isValidPosition(newRow, newCol) && gameState.board[newRow][newCol] === player) {
                    count++;
                    winningPositions.push({row: newRow, col: newCol});
                } else {
                    break;
                }
            }

            // 反方向检查
            for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
                const newRow = row - dx * i;
                const newCol = col - dy * i;
                if (isValidPosition(newRow, newCol) && gameState.board[newRow][newCol] === player) {
                    count++;
                    winningPositions.unshift({row: newRow, col: newCol});
                } else {
                    break;
                }
            }

            if (count >= CONFIG.WIN_COUNT) {
                highlightWinningPieces(winningPositions);
                return true;
            }
        }
        return false;
    }

    // 位置验证函数
    function isValidPosition(row, col) {
        return row >= 0 && row < CONFIG.BOARD_SIZE && col >= 0 && col < CONFIG.BOARD_SIZE;
    }

    // 检查平局
    function checkDraw() {
        return gameState.board.every(row => row.every(cell => cell !== null));
    }

    // 高亮获胜棋子
    function highlightWinningPieces(positions) {
        positions.forEach((pos, index) => {
            setTimeout(() => {
                const cell = elements.board.querySelector(`[data-row="${pos.row}"][data-col="${pos.col}"]`);
                if (cell) {
                    cell.classList.add('winning', 'winning-line');
                }
            }, index * 100);
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // AI落子 - 修复版本
    // 改进后的AI移动函数
    function aiMove() {
        if (!gameState.gameActive) return;
        
        let bestMove = null;
    
        if (gameState.level === 'easy') {
            bestMove = getEasyAIMove();
        } else if (gameState.level === 'medium') {
            bestMove = getMediumAIMove();
        } else if (gameState.level === 'hard') {
            bestMove = getHardAIMove();
        }
    
        if (bestMove) {
            makeMove(bestMove.row, bestMove.col, gameState.currentPlayer);
            gameState.gameHistory.push({row: bestMove.row, col: bestMove.col, player: gameState.currentPlayer});
    
            // 检查AI是否获胜
            if (checkWin(bestMove.row, bestMove.col, gameState.currentPlayer)) {
                elements.message.textContent = '🤖 AI获胜了！';
                gameState.aiWins++;
                updateScore();
                gameState.gameActive = false;
                
                setTimeout(() => {
                    checkMatchEnd();
                }, 2000);
                return;
            }
    
            // 检查是否平局
            if (checkDraw()) {
                elements.message.textContent = '⚖️ 平局！';
                gameState.gameActive = false;
                setTimeout(nextGame, 2000);
                return;
            }
    
            // 切换回玩家回合
            gameState.currentPlayer = 'black';
            elements.message.textContent = '轮到你落子 (黑棋)';
        }
    }
    
    // 改进的初级AI - 有基本策略
    function getEasyAIMove() {
    // 1. 如果能直接获胜，就获胜
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    gameState.board[i][j] = 'white';
    if (checkWin(i, j, 'white')) {
    gameState.board[i][j] = null;
    return {row: i, col: j};
    }
    gameState.board[i][j] = null;
    }
    }
    }
    
    // 2. 如果对手能获胜，就阻止
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    gameState.board[i][j] = 'black';
    if (checkWin(i, j, 'black')) {
    gameState.board[i][j] = null;
    return {row: i, col: j};
    }
    gameState.board[i][j] = null;
    }
    }
    }
    
    // 3. 优先选择中心区域
    const center = Math.floor(CONFIG.BOARD_SIZE / 2);
    const centerArea = [];
    for (let i = center - 2; i <= center + 2; i++) {
    for (let j = center - 2; j <= center + 2; j++) {
    if (isValidPosition(i, j) && !gameState.board[i][j]) {
    centerArea.push({row: i, col: j});
    }
    }
    }
    
    if (centerArea.length > 0) {
    return centerArea[Math.floor(Math.random() * centerArea.length)];
    }
    
    // 4. 随机选择
    return getRandomMove();
    }
    
    // 改进的中级AI
    function getMediumAIMove() {
    // 使用评分系统
    let bestScore = -Infinity;
    let bestMove = null;
    
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    let score = evaluatePosition(i, j, 'white') - evaluatePosition(i, j, 'black');
    
    // 添加位置权重
    const center = Math.floor(CONFIG.BOARD_SIZE / 2);
    const distanceFromCenter = Math.abs(i - center) + Math.abs(j - center);
    score += (CONFIG.BOARD_SIZE - distanceFromCenter) * 0.1;
    
    if (score > bestScore) {
    bestScore = score;
    bestMove = {row: i, col: j};
    }
    }
    }
    }
    
    return bestMove || getRandomMove();
    }
    
    // 高级AI - 使用Minimax算法
    function getHardAIMove() {
    const depth = 3; // 搜索深度
    let bestScore = -Infinity;
    let bestMove = null;
    
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    gameState.board[i][j] = 'white';
    let score = minimax(depth - 1, false, -Infinity, Infinity);
    gameState.board[i][j] = null;
    
    if (score > bestScore) {
    bestScore = score;
    bestMove = {row: i, col: j};
    }
    }
    }
    }
    
    return bestMove || getRandomMove();
    }
    
    // Minimax算法实现
    function minimax(depth, isMaximizing, alpha, beta) {
    // 检查游戏结束状态
    const winner = checkGameEnd();
    if (winner === 'white') return 1000 + depth;
    if (winner === 'black') return -1000 - depth;
    if (winner === 'draw' || depth === 0) return evaluateBoard();
    
    if (isMaximizing) {
    let maxScore = -Infinity;
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    gameState.board[i][j] = 'white';
    let score = minimax(depth - 1, false, alpha, beta);
    gameState.board[i][j] = null;
    maxScore = Math.max(score, maxScore);
    alpha = Math.max(alpha, score);
    if (beta <= alpha) break;
    }
    }
    if (beta <= alpha) break;
    }
    return maxScore;
    } else {
    let minScore = Infinity;
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (!gameState.board[i][j]) {
    gameState.board[i][j] = 'black';
    let score = minimax(depth - 1, true, alpha, beta);
    gameState.board[i][j] = null;
    minScore = Math.min(score, minScore);
    beta = Math.min(beta, score);
    if (beta <= alpha) break;
    }
    }
    if (beta <= alpha) break;
    }
    return minScore;
    }
    }
    
    // 评估棋盘整体状态
    function evaluateBoard() {
    let score = 0;
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (gameState.board[i][j] === 'white') {
    score += evaluatePosition(i, j, 'white');
    } else if (gameState.board[i][j] === 'black') {
    score -= evaluatePosition(i, j, 'black');
    }
    }
    }
    return score;
    }
    
    // 评估某个位置的价值
    function evaluatePosition(row, col, player) {
    let score = 0;
    const directions = [[0,1], [1,0], [1,1], [1,-1]];
    
    for (const [dx, dy] of directions) {
    score += evaluateLine(row, col, dx, dy, player);
    }
    return score;
    }
    
    // 评估某个方向的连线价值
    function evaluateLine(row, col, dx, dy, player) {
    let score = 0;
    let count = 0;
    let blocked = 0;
    
    // 检查正方向
    for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
    const newRow = row + dx * i;
    const newCol = col + dy * i;
    if (!isValidPosition(newRow, newCol)) {
    blocked++;
    break;
    }
    if (gameState.board[newRow][newCol] === player) {
    count++;
    } else if (gameState.board[newRow][newCol] !== null) {
    blocked++;
    break;
    } else {
    break;
    }
    }
    
    // 检查反方向
    for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
    const newRow = row - dx * i;
    const newCol = col - dy * i;
    if (!isValidPosition(newRow, newCol)) {
    blocked++;
    break;
    }
    if (gameState.board[newRow][newCol] === player) {
    count++;
    } else if (gameState.board[newRow][newCol] !== null) {
    blocked++;
    break;
    } else {
    break;
    }
    }
    
    // 根据连子数和阻挡情况计算分数
    if (blocked >= 2) return 0;
    
    switch (count) {
    case 0: return 1;
    case 1: return 10;
    case 2: return 100;
    case 3: return 1000;
    case 4: return 10000;
    default: return 0;
    }
    }
    
    // 检查游戏结束状态
    function checkGameEnd() {
    // 检查是否有获胜者
    for (let i = 0; i < CONFIG.BOARD_SIZE; i++) {
    for (let j = 0; j < CONFIG.BOARD_SIZE; j++) {
    if (gameState.board[i][j]) {
    if (checkWinSimple(i, j, gameState.board[i][j])) {
    return gameState.board[i][j];
    }
    }
    }
    }
    
    // 检查是否平局
    if (checkDraw()) return 'draw';
    
    return null;
    }
    
    // 简单的胜利检查（不触发高亮）
    function checkWinSimple(row, col, player) {
    const directions = [[0,1], [1,0], [1,1], [1,-1]];
    
    for (const [dx, dy] of directions) {
    let count = 1;
    
    // 正方向
    for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
    const newRow = row + dx * i;
    const newCol = col + dy * i;
    if (isValidPosition(newRow, newCol) && gameState.board[newRow][newCol] === player) {
    count++;
    } else break;
    }
    
    // 反方向
    for (let i = 1; i < CONFIG.WIN_COUNT; i++) {
    const newRow = row - dx * i;
    const newCol = col - dy * i;
    if (isValidPosition(newRow, newCol) && gameState.board[newRow][newCol] === player) {
    count++;
    } else break;
    }
    
    if (count >= CONFIG.WIN_COUNT) return true;
    }
    return false;
    }
    
    // 更新难度选择的事件监听器
    elements.levelSelect.addEventListener('change', (e) => {
    gameState.level = e.target.value;
    let levelText = '';
    switch(gameState.level) {
    case 'easy': levelText = '初级'; break;
    case 'medium': levelText = '中级'; break;
    case 'hard': levelText = '高级'; break;
    }
    elements.level.textContent = levelText;
    alert(`已切换到${levelText}难度`);
    resetMatch();
    });

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
        });
    }

    // 清除获胜高亮
    function clearWinningHighlight() {
        const winningCells = elements.board.querySelectorAll('.winning');
        winningCells.forEach(cell => {
            cell.classList.remove('winning', 'winning-line');
    });